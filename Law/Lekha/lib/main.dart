import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'pages/language_selection_page.dart';
import 'pages/main_app.dart';
import 'services/language_service.dart';
import 'services/auth_service.dart';

void main() {
  runApp(LekhaApp());
}

class LekhaApp extends StatelessWidget {
  const LekhaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => LanguageService()),
        ChangeNotifierProvider(create: (context) => AuthService()),
      ],
      child: MaterialApp(
        title: 'Lekha - Legal Management',
        theme: ThemeData(
          brightness: Brightness.dark,
          scaffoldBackgroundColor: Color(0xFF0D0D0D),
          primaryColor: Color(0xFFFFFFFF),
          fontFamily: 'Noto Sans Devanagari',
          colorScheme: ColorScheme.dark(
            primary: Color(0xFFFF9933),
            secondary: Color(0xFFFF7722),
            surface: Color(0xFF1A1A1A),
          ),
          appBarTheme: AppBarTheme(
            backgroundColor: Color(0xFF1A1A1A),
            elevation: 0,
            iconTheme: IconThemeData(color: Colors.white),
            titleTextStyle: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFFFF9933),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        home: AuthWrapper(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  AuthWrapperState createState() => AuthWrapperState();
}

class AuthWrapperState extends State<AuthWrapper> {
  @override
  void initState() {
    super.initState();
    // Initialize auth service
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthService>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        print(
          'AuthWrapper: isLoading=${authService.isLoading}, isAuthenticated=${authService.isAuthenticated}',
        );
        print('AuthWrapper AuthService instance: ${authService.hashCode}');
        if (authService.isLoading) {
          return Scaffold(
            backgroundColor: Color(0xFF0D0D0D),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(0xFFFF9933),
                          Color(0xFFFFFFFF),
                          Color(0xFF138808),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: Icon(
                      Icons.gavel_rounded,
                      size: 40,
                      color: Color(0xFF0D0D0D),
                    ),
                  ),
                  SizedBox(height: 32),
                  Text(
                    'लेखा',
                    style: TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.w300,
                      color: Colors.white,
                      letterSpacing: 2,
                    ),
                  ),
                  Text(
                    'LEKHA',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.white.withValues(alpha: 0.6),
                      letterSpacing: 4,
                    ),
                  ),
                  SizedBox(height: 32),
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(0xFFFF9933),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        if (authService.isAuthenticated) {
          print('User is authenticated, showing MainApp');
          return MainApp();
        } else {
          print('User is not authenticated, showing LanguageSelectionPage');
          // Check if user has selected language before
          return Consumer<LanguageService>(
            builder: (context, languageService, child) {
              // For now, always show language selection first
              // In a real app, you'd check if language was previously selected
              return LanguageSelectionPage();
            },
          );
        }
      },
    );
  }
}
