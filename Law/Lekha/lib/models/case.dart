import 'package:flutter/foundation.dart';

enum CaseType { civil, criminal, family, corporate, tax }

enum CaseStatus { active, closed, pendingHearing, archived }

enum HearingStatus { adjourned, heard, nextDateGiven }

class Case {
  final String caseId;
  final CaseType caseType;
  final CaseStatus status;
  final Filing filing;
  final Registration registration;
  final String? crnNumber;
  final String? uniqueId;
  final List<AssignedUser> assignedUsers;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ClientDetails clientDetails;
  final List<CaseResearchFinding> caseResearchFindings;
  final List<Hearing> hearings;
  final List<CaseHistoryEvent> caseHistory;
  final List<AISummary> aiSummaries;

  Case({
    required this.caseId,
    required this.caseType,
    required this.status,
    required this.filing,
    required this.registration,
    this.crnNumber,
    this.uniqueId,
    required this.assignedUsers,
    required this.createdAt,
    required this.updatedAt,
    required this.clientDetails,
    required this.caseResearchFindings,
    required this.hearings,
    required this.caseHistory,
    required this.aiSummaries,
  });

  factory Case.fromJson(Map<String, dynamic> json) {
    return Case(
      caseId: json['caseId'],
      caseType: CaseType.values.firstWhere(
        (e) => e.toString().split('.').last == json['caseType'],
      ),
      status: CaseStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      filing: Filing.fromJson(json['filing']),
      registration: Registration.fromJson(json['registration']),
      crnNumber: json['crnNumber'],
      uniqueId: json['uniqueId'],
      assignedUsers: (json['assignedUsers'] as List)
          .map((e) => AssignedUser.fromJson(e))
          .toList(),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      clientDetails: ClientDetails.fromJson(json['clientDetails']),
      caseResearchFindings: (json['caseResearchFindings'] as List)
          .map((e) => CaseResearchFinding.fromJson(e))
          .toList(),
      hearings: (json['hearings'] as List)
          .map((e) => Hearing.fromJson(e))
          .toList(),
      caseHistory: (json['caseHistory'] as List)
          .map((e) => CaseHistoryEvent.fromJson(e))
          .toList(),
      aiSummaries: (json['aiSummaries'] as List)
          .map((e) => AISummary.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'caseId': caseId,
      'caseType': caseType.toString().split('.').last,
      'status': status.toString().split('.').last,
      'filing': filing.toJson(),
      'registration': registration.toJson(),
      'crnNumber': crnNumber,
      'uniqueId': uniqueId,
      'assignedUsers': assignedUsers.map((e) => e.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'clientDetails': clientDetails.toJson(),
      'caseResearchFindings': caseResearchFindings.map((e) => e.toJson()).toList(),
      'hearings': hearings.map((e) => e.toJson()).toList(),
      'caseHistory': caseHistory.map((e) => e.toJson()).toList(),
      'aiSummaries': aiSummaries.map((e) => e.toJson()).toList(),
    };
  }

  String get displayNumber => registration.number ?? filing.number;
  
  Hearing? get nextHearing {
    final upcomingHearings = hearings
        .where((h) => h.date.isAfter(DateTime.now()))
        .toList();
    upcomingHearings.sort((a, b) => a.date.compareTo(b.date));
    return upcomingHearings.isNotEmpty ? upcomingHearings.first : null;
  }
}

class Filing {
  final String number;
  final DateTime date;

  Filing({required this.number, required this.date});

  factory Filing.fromJson(Map<String, dynamic> json) {
    return Filing(
      number: json['number'],
      date: DateTime.parse(json['date']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'date': date.toIso8601String().split('T')[0],
    };
  }
}

class Registration {
  final String? number;
  final DateTime? date;

  Registration({this.number, this.date});

  factory Registration.fromJson(Map<String, dynamic> json) {
    return Registration(
      number: json['number'],
      date: json['date'] != null ? DateTime.parse(json['date']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'date': date?.toIso8601String().split('T')[0],
    };
  }
}

class AssignedUser {
  final String userId;
  final String role;

  AssignedUser({required this.userId, required this.role});

  factory AssignedUser.fromJson(Map<String, dynamic> json) {
    return AssignedUser(
      userId: json['userId'],
      role: json['role'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'role': role,
    };
  }
}

class ClientDetails {
  final String name;
  final ClientContact contact;
  final List<ClientDocument> documents;

  ClientDetails({
    required this.name,
    required this.contact,
    required this.documents,
  });

  factory ClientDetails.fromJson(Map<String, dynamic> json) {
    return ClientDetails(
      name: json['name'],
      contact: ClientContact.fromJson(json['contact']),
      documents: (json['documents'] as List)
          .map((e) => ClientDocument.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'contact': contact.toJson(),
      'documents': documents.map((e) => e.toJson()).toList(),
    };
  }
}

class ClientContact {
  final String email;
  final String phone;

  ClientContact({required this.email, required this.phone});

  factory ClientContact.fromJson(Map<String, dynamic> json) {
    return ClientContact(
      email: json['email'],
      phone: json['phone'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'phone': phone,
    };
  }
}

class ClientDocument {
  final String documentId;
  final String name;
  final String url;
  final String uploadedByUserId;
  final DateTime uploadedAt;
  final Map<String, dynamic> metadata;

  ClientDocument({
    required this.documentId,
    required this.name,
    required this.url,
    required this.uploadedByUserId,
    required this.uploadedAt,
    required this.metadata,
  });

  factory ClientDocument.fromJson(Map<String, dynamic> json) {
    return ClientDocument(
      documentId: json['documentId'],
      name: json['name'],
      url: json['url'],
      uploadedByUserId: json['uploadedByUserId'],
      uploadedAt: DateTime.parse(json['uploadedAt']),
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'documentId': documentId,
      'name': name,
      'url': url,
      'uploadedByUserId': uploadedByUserId,
      'uploadedAt': uploadedAt.toIso8601String(),
      'metadata': metadata,
    };
  }
}

class CaseResearchFinding {
  final String findingId;
  final String title;
  final String summary;
  final String? sourceUrl;
  final String addedByUserId;
  final DateTime addedAt;

  CaseResearchFinding({
    required this.findingId,
    required this.title,
    required this.summary,
    this.sourceUrl,
    required this.addedByUserId,
    required this.addedAt,
  });

  factory CaseResearchFinding.fromJson(Map<String, dynamic> json) {
    return CaseResearchFinding(
      findingId: json['findingId'],
      title: json['title'],
      summary: json['summary'],
      sourceUrl: json['sourceUrl'],
      addedByUserId: json['addedByUserId'],
      addedAt: DateTime.parse(json['addedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'findingId': findingId,
      'title': title,
      'summary': summary,
      'sourceUrl': sourceUrl,
      'addedByUserId': addedByUserId,
      'addedAt': addedAt.toIso8601String(),
    };
  }
}

class Hearing {
  final String hearingId;
  final DateTime date;
  final String time;
  final String court;
  final String judge;
  final HearingStatus status;
  final NextHearing? nextHearing;
  final List<HearingNote> notesAndComments;

  Hearing({
    required this.hearingId,
    required this.date,
    required this.time,
    required this.court,
    required this.judge,
    required this.status,
    this.nextHearing,
    required this.notesAndComments,
  });

  factory Hearing.fromJson(Map<String, dynamic> json) {
    return Hearing(
      hearingId: json['hearingId'],
      date: DateTime.parse(json['date']),
      time: json['time'],
      court: json['court'],
      judge: json['judge'],
      status: HearingStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      nextHearing: json['nextHearing'] != null
          ? NextHearing.fromJson(json['nextHearing'])
          : null,
      notesAndComments: (json['notesAndComments'] as List)
          .map((e) => HearingNote.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hearingId': hearingId,
      'date': date.toIso8601String().split('T')[0],
      'time': time,
      'court': court,
      'judge': judge,
      'status': status.toString().split('.').last,
      'nextHearing': nextHearing?.toJson(),
      'notesAndComments': notesAndComments.map((e) => e.toJson()).toList(),
    };
  }
}

class NextHearing {
  final DateTime date;
  final String time;

  NextHearing({required this.date, required this.time});

  factory NextHearing.fromJson(Map<String, dynamic> json) {
    return NextHearing(
      date: DateTime.parse(json['date']),
      time: json['time'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String().split('T')[0],
      'time': time,
    };
  }
}

class HearingNote {
  final String noteId;
  final String text;
  final String addedByUserId;
  final DateTime addedAt;

  HearingNote({
    required this.noteId,
    required this.text,
    required this.addedByUserId,
    required this.addedAt,
  });

  factory HearingNote.fromJson(Map<String, dynamic> json) {
    return HearingNote(
      noteId: json['noteId'],
      text: json['text'],
      addedByUserId: json['addedByUserId'],
      addedAt: DateTime.parse(json['addedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'noteId': noteId,
      'text': text,
      'addedByUserId': addedByUserId,
      'addedAt': addedAt.toIso8601String(),
    };
  }
}

class CaseHistoryEvent {
  final String event;
  final String description;
  final String changedByUserId;
  final DateTime changedAt;

  CaseHistoryEvent({
    required this.event,
    required this.description,
    required this.changedByUserId,
    required this.changedAt,
  });

  factory CaseHistoryEvent.fromJson(Map<String, dynamic> json) {
    return CaseHistoryEvent(
      event: json['event'],
      description: json['description'],
      changedByUserId: json['changedByUserId'],
      changedAt: DateTime.parse(json['changedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'event': event,
      'description': description,
      'changedByUserId': changedByUserId,
      'changedAt': changedAt.toIso8601String(),
    };
  }
}

class AISummary {
  final String summaryId;
  final String type;
  final DateTime generatedAt;
  final String content;
  final bool sharedWithClient;

  AISummary({
    required this.summaryId,
    required this.type,
    required this.generatedAt,
    required this.content,
    required this.sharedWithClient,
  });

  factory AISummary.fromJson(Map<String, dynamic> json) {
    return AISummary(
      summaryId: json['summaryId'],
      type: json['type'],
      generatedAt: DateTime.parse(json['generatedAt']),
      content: json['content'],
      sharedWithClient: json['sharedWithClient'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'summaryId': summaryId,
      'type': type,
      'generatedAt': generatedAt.toIso8601String(),
      'content': content,
      'sharedWithClient': sharedWithClient,
    };
  }
}
