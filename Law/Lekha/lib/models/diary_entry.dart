import 'package:flutter/foundation.dart';

enum DiaryEntryType { hearing, meeting, deadline, task, personal, note }

enum DiaryEntryStatus { scheduled, completed, cancelled, pending }

class DiaryEntry {
  final String entryId;
  final String userId;
  final String? caseId;
  final DiaryEntryType type;
  final String title;
  final String description;
  final DateTime date;
  final String? time;
  final String? location;
  final DiaryEntryStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  DiaryEntry({
    required this.entryId,
    required this.userId,
    this.caseId,
    required this.type,
    required this.title,
    required this.description,
    required this.date,
    this.time,
    this.location,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DiaryEntry.fromJson(Map<String, dynamic> json) {
    return DiaryEntry(
      entryId: json['entryId'],
      userId: json['userId'],
      caseId: json['caseId'],
      type: DiaryEntryType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
      ),
      title: json['title'],
      description: json['description'],
      date: DateTime.parse(json['date']),
      time: json['time'],
      location: json['location'],
      status: DiaryEntryStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entryId': entryId,
      'userId': userId,
      'caseId': caseId,
      'type': type.toString().split('.').last,
      'title': title,
      'description': description,
      'date': date.toIso8601String().split('T')[0],
      'time': time,
      'location': location,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  bool get isToday {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  bool get isUpcoming {
    return date.isAfter(DateTime.now());
  }

  bool get isPast {
    return date.isBefore(DateTime.now());
  }
}
