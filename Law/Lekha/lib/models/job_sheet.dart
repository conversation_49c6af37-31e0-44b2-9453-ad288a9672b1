import 'package:flutter/foundation.dart';

enum JobSheetStatus { assigned, inProgress, completed, underReview }

class JobSheet {
  final String jobSheetId;
  final String internId;
  final String assignedByAdvocateId;
  final String? caseId;
  final String title;
  final String description;
  final DateTime assignedDate;
  final DateTime dueDate;
  final JobSheetStatus status;
  final List<Deliverable> deliverables;
  final String? feedback;
  final String? feedbackByAdvocateId;
  final DateTime? feedbackGivenAt;
  final List<JobSheetComment> comments;
  final DateTime createdAt;
  final DateTime updatedAt;

  JobSheet({
    required this.jobSheetId,
    required this.internId,
    required this.assignedByAdvocateId,
    this.caseId,
    required this.title,
    required this.description,
    required this.assignedDate,
    required this.dueDate,
    required this.status,
    required this.deliverables,
    this.feedback,
    this.feedbackByAdvocateId,
    this.feedbackGivenAt,
    required this.comments,
    required this.createdAt,
    required this.updatedAt,
  });

  factory JobSheet.fromJson(Map<String, dynamic> json) {
    return JobSheet(
      jobSheetId: json['jobSheetId'],
      internId: json['internId'],
      assignedByAdvocateId: json['assignedByAdvocateId'],
      caseId: json['caseId'],
      title: json['title'],
      description: json['description'],
      assignedDate: DateTime.parse(json['assignedDate']),
      dueDate: DateTime.parse(json['dueDate']),
      status: JobSheetStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      deliverables: (json['deliverables'] as List)
          .map((e) => Deliverable.fromJson(e))
          .toList(),
      feedback: json['feedback'],
      feedbackByAdvocateId: json['feedbackByAdvocateId'],
      feedbackGivenAt: json['feedbackGivenAt'] != null
          ? DateTime.parse(json['feedbackGivenAt'])
          : null,
      comments: (json['comments'] as List)
          .map((e) => JobSheetComment.fromJson(e))
          .toList(),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'jobSheetId': jobSheetId,
      'internId': internId,
      'assignedByAdvocateId': assignedByAdvocateId,
      'caseId': caseId,
      'title': title,
      'description': description,
      'assignedDate': assignedDate.toIso8601String(),
      'dueDate': dueDate.toIso8601String().split('T')[0],
      'status': status.toString().split('.').last,
      'deliverables': deliverables.map((e) => e.toJson()).toList(),
      'feedback': feedback,
      'feedbackByAdvocateId': feedbackByAdvocateId,
      'feedbackGivenAt': feedbackGivenAt?.toIso8601String(),
      'comments': comments.map((e) => e.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  bool get isOverdue {
    return DateTime.now().isAfter(dueDate) && status != JobSheetStatus.completed;
  }

  bool get isDueSoon {
    final now = DateTime.now();
    final daysDifference = dueDate.difference(now).inDays;
    return daysDifference <= 3 && daysDifference >= 0 && status != JobSheetStatus.completed;
  }

  int get daysUntilDue {
    return dueDate.difference(DateTime.now()).inDays;
  }
}

class Deliverable {
  final String deliverableId;
  final String name;
  final String url;
  final DateTime submittedAt;

  Deliverable({
    required this.deliverableId,
    required this.name,
    required this.url,
    required this.submittedAt,
  });

  factory Deliverable.fromJson(Map<String, dynamic> json) {
    return Deliverable(
      deliverableId: json['deliverableId'],
      name: json['name'],
      url: json['url'],
      submittedAt: DateTime.parse(json['submittedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'deliverableId': deliverableId,
      'name': name,
      'url': url,
      'submittedAt': submittedAt.toIso8601String(),
    };
  }
}

class JobSheetComment {
  final String commentId;
  final String text;
  final String addedByUserId;
  final DateTime addedAt;

  JobSheetComment({
    required this.commentId,
    required this.text,
    required this.addedByUserId,
    required this.addedAt,
  });

  factory JobSheetComment.fromJson(Map<String, dynamic> json) {
    return JobSheetComment(
      commentId: json['commentId'],
      text: json['text'],
      addedByUserId: json['addedByUserId'],
      addedAt: DateTime.parse(json['addedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'commentId': commentId,
      'text': text,
      'addedByUserId': addedByUserId,
      'addedAt': addedAt.toIso8601String(),
    };
  }
}
