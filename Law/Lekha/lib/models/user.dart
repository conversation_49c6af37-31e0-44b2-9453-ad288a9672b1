enum UserType { advocate, jrAdvocate, intern }

enum AdvocateStatus { active, pendingApproval }

enum LicenseStatus { student, yes, no }

enum InternRequestStatus { pending, accepted, rejected }

enum CompanyType { partnership, soleProprietorship, llp, privateLimited }

class User {
  final String userId;
  final String email;
  final String? passwordHash;
  final UserType userType;
  final String languagePreference;
  final DateTime createdAt;
  final DateTime? lastLogin;
  final UserProfile profile;
  final AccessControl accessControl;

  User({
    required this.userId,
    required this.email,
    this.passwordHash,
    required this.userType,
    required this.languagePreference,
    required this.createdAt,
    this.lastLogin,
    required this.profile,
    required this.accessControl,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      userId: json['userId'],
      email: json['email'],
      passwordHash: json['passwordHash'],
      userType: UserType.values.firstWhere(
        (e) => e.toString().split('.').last == json['userType'],
      ),
      languagePreference: json['languagePreference'],
      createdAt: DateTime.parse(json['createdAt']),
      lastLogin: json['lastLogin'] != null
          ? DateTime.parse(json['lastLogin'])
          : null,
      profile: UserProfile.fromJson(json['profile']),
      accessControl: AccessControl.fromJson(json['accessControl']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'email': email,
      'passwordHash': passwordHash,
      'userType': userType.toString().split('.').last,
      'languagePreference': languagePreference,
      'createdAt': createdAt.toIso8601String(),
      'lastLogin': lastLogin?.toIso8601String(),
      'profile': profile.toJson(),
      'accessControl': accessControl.toJson(),
    };
  }
}

class UserProfile {
  final Name name;
  final Contact contact;
  final Address address;

  // Advocate/Jr. Advocate Specific
  final String? advocateLicenseNumber;
  final String? officeName;
  final int? numberOfEmployees;
  final CompanyType? registeredCompanyType;
  final String? roleAtCompany;
  final AdvocateStatus? advocateStatus;

  // Intern Specific
  final int? age;
  final String? college;
  final String? resumeUrl;
  final LicenseStatus? licensesEnrolledStatus;
  final List<InternRequest>? internRequests;

  UserProfile({
    required this.name,
    required this.contact,
    required this.address,
    this.advocateLicenseNumber,
    this.officeName,
    this.numberOfEmployees,
    this.registeredCompanyType,
    this.roleAtCompany,
    this.advocateStatus,
    this.age,
    this.college,
    this.resumeUrl,
    this.licensesEnrolledStatus,
    this.internRequests,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      name: Name.fromJson(json['name']),
      contact: Contact.fromJson(json['contact']),
      address: Address.fromJson(json['address']),
      advocateLicenseNumber: json['advocateLicenseNumber'],
      officeName: json['officeName'],
      numberOfEmployees: json['numberOfEmployees'],
      registeredCompanyType: json['registeredCompanyType'] != null
          ? CompanyType.values.firstWhere(
              (e) =>
                  e.toString().split('.').last == json['registeredCompanyType'],
            )
          : null,
      roleAtCompany: json['roleAtCompany'],
      advocateStatus: json['advocateStatus'] != null
          ? AdvocateStatus.values.firstWhere(
              (e) => e.toString().split('.').last == json['advocateStatus'],
            )
          : null,
      age: json['age'],
      college: json['college'],
      resumeUrl: json['resumeUrl'],
      licensesEnrolledStatus: json['licensesEnrolledStatus'] != null
          ? LicenseStatus.values.firstWhere(
              (e) =>
                  e.toString().split('.').last ==
                  json['licensesEnrolledStatus'],
            )
          : null,
      internRequests: json['internRequests'] != null
          ? (json['internRequests'] as List)
                .map((e) => InternRequest.fromJson(e))
                .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name.toJson(),
      'contact': contact.toJson(),
      'address': address.toJson(),
      'advocateLicenseNumber': advocateLicenseNumber,
      'officeName': officeName,
      'numberOfEmployees': numberOfEmployees,
      'registeredCompanyType': registeredCompanyType
          ?.toString()
          .split('.')
          .last,
      'roleAtCompany': roleAtCompany,
      'advocateStatus': advocateStatus?.toString().split('.').last,
      'age': age,
      'college': college,
      'resumeUrl': resumeUrl,
      'licensesEnrolledStatus': licensesEnrolledStatus
          ?.toString()
          .split('.')
          .last,
      'internRequests': internRequests?.map((e) => e.toJson()).toList(),
    };
  }
}

class Name {
  final String firstName;
  final String lastName;

  Name({required this.firstName, required this.lastName});

  factory Name.fromJson(Map<String, dynamic> json) {
    return Name(firstName: json['firstName'], lastName: json['lastName']);
  }

  Map<String, dynamic> toJson() {
    return {'firstName': firstName, 'lastName': lastName};
  }

  String get fullName => '$firstName $lastName';
}

class Contact {
  final String email;
  final String mobile;
  final String? whatsapp;

  Contact({required this.email, required this.mobile, this.whatsapp});

  factory Contact.fromJson(Map<String, dynamic> json) {
    return Contact(
      email: json['email'],
      mobile: json['mobile'],
      whatsapp: json['whatsapp'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'email': email, 'mobile': mobile, 'whatsapp': whatsapp};
  }
}

class Address {
  final String street;
  final String city;
  final String state;
  final String zipCode;

  Address({
    required this.street,
    required this.city,
    required this.state,
    required this.zipCode,
  });

  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      street: json['street'],
      city: json['city'],
      state: json['state'],
      zipCode: json['zipCode'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'street': street, 'city': city, 'state': state, 'zipCode': zipCode};
  }

  String get fullAddress => '$street, $city, $state - $zipCode';
}

class InternRequest {
  final String advocateId;
  final InternRequestStatus status;
  final DateTime requestedAt;
  final DateTime? acceptedAt;

  InternRequest({
    required this.advocateId,
    required this.status,
    required this.requestedAt,
    this.acceptedAt,
  });

  factory InternRequest.fromJson(Map<String, dynamic> json) {
    return InternRequest(
      advocateId: json['advocateId'],
      status: InternRequestStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      requestedAt: DateTime.parse(json['requestedAt']),
      acceptedAt: json['acceptedAt'] != null
          ? DateTime.parse(json['acceptedAt'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'advocateId': advocateId,
      'status': status.toString().split('.').last,
      'requestedAt': requestedAt.toIso8601String(),
      'acceptedAt': acceptedAt?.toIso8601String(),
    };
  }
}

class AccessControl {
  final bool canAddCases;
  final bool canRemoveCases;
  final bool canViewAnalytics;
  final bool canAccessClientDetails;
  final bool canShareAIResults;
  final bool canGrantInternAccess;
  final bool canAcceptInternRequests;
  final bool canAccessVoiceFeatures;
  final bool canAddNotesToHearings;
  final bool canManageJobSheets;

  AccessControl({
    required this.canAddCases,
    required this.canRemoveCases,
    required this.canViewAnalytics,
    required this.canAccessClientDetails,
    required this.canShareAIResults,
    required this.canGrantInternAccess,
    required this.canAcceptInternRequests,
    required this.canAccessVoiceFeatures,
    required this.canAddNotesToHearings,
    required this.canManageJobSheets,
  });

  factory AccessControl.fromJson(Map<String, dynamic> json) {
    return AccessControl(
      canAddCases: json['canAddCases'] ?? false,
      canRemoveCases: json['canRemoveCases'] ?? false,
      canViewAnalytics: json['canViewAnalytics'] ?? false,
      canAccessClientDetails: json['canAccessClientDetails'] ?? false,
      canShareAIResults: json['canShareAIResults'] ?? false,
      canGrantInternAccess: json['canGrantInternAccess'] ?? false,
      canAcceptInternRequests: json['canAcceptInternRequests'] ?? false,
      canAccessVoiceFeatures: json['canAccessVoiceFeatures'] ?? false,
      canAddNotesToHearings: json['canAddNotesToHearings'] ?? false,
      canManageJobSheets: json['canManageJobSheets'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'canAddCases': canAddCases,
      'canRemoveCases': canRemoveCases,
      'canViewAnalytics': canViewAnalytics,
      'canAccessClientDetails': canAccessClientDetails,
      'canShareAIResults': canShareAIResults,
      'canGrantInternAccess': canGrantInternAccess,
      'canAcceptInternRequests': canAcceptInternRequests,
      'canAccessVoiceFeatures': canAccessVoiceFeatures,
      'canAddNotesToHearings': canAddNotesToHearings,
      'canManageJobSheets': canManageJobSheets,
    };
  }

  // Factory methods for different user types
  factory AccessControl.forAdvocate() {
    return AccessControl(
      canAddCases: true,
      canRemoveCases: true,
      canViewAnalytics: true,
      canAccessClientDetails: true,
      canShareAIResults: true,
      canGrantInternAccess: true,
      canAcceptInternRequests: true,
      canAccessVoiceFeatures: true,
      canAddNotesToHearings: true,
      canManageJobSheets: true,
    );
  }

  factory AccessControl.forJrAdvocate() {
    return AccessControl(
      canAddCases: true,
      canRemoveCases: false,
      canViewAnalytics: true,
      canAccessClientDetails: true,
      canShareAIResults: true,
      canGrantInternAccess: false,
      canAcceptInternRequests: false,
      canAccessVoiceFeatures: true,
      canAddNotesToHearings: true,
      canManageJobSheets: false,
    );
  }

  factory AccessControl.forIntern() {
    return AccessControl(
      canAddCases: false,
      canRemoveCases: false,
      canViewAnalytics: false,
      canAccessClientDetails: true,
      canShareAIResults: false,
      canGrantInternAccess: false,
      canAcceptInternRequests: false,
      canAccessVoiceFeatures: true,
      canAddNotesToHearings: true,
      canManageJobSheets: false,
    );
  }
}
