import 'package:flutter/foundation.dart';

class YellowPagesEntry {
  final String entryId;
  final String name;
  final String category;
  final YellowPagesContactDetails contactDetails;
  final List<String> specialties;
  final String? website;
  final String description;
  final double? rating;
  final String addedByUserId;
  final DateTime createdAt;

  YellowPagesEntry({
    required this.entryId,
    required this.name,
    required this.category,
    required this.contactDetails,
    required this.specialties,
    this.website,
    required this.description,
    this.rating,
    required this.addedByUserId,
    required this.createdAt,
  });

  factory YellowPagesEntry.fromJson(Map<String, dynamic> json) {
    return YellowPagesEntry(
      entryId: json['entryId'],
      name: json['name'],
      category: json['category'],
      contactDetails: YellowPagesContactDetails.fromJson(json['contactDetails']),
      specialties: List<String>.from(json['specialties']),
      website: json['website'],
      description: json['description'],
      rating: json['rating']?.toDouble(),
      addedByUserId: json['addedByUserId'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entryId': entryId,
      'name': name,
      'category': category,
      'contactDetails': contactDetails.toJson(),
      'specialties': specialties,
      'website': website,
      'description': description,
      'rating': rating,
      'addedByUserId': addedByUserId,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class YellowPagesContactDetails {
  final String phone;
  final String email;
  final String address;

  YellowPagesContactDetails({
    required this.phone,
    required this.email,
    required this.address,
  });

  factory YellowPagesContactDetails.fromJson(Map<String, dynamic> json) {
    return YellowPagesContactDetails(
      phone: json['phone'],
      email: json['email'],
      address: json['address'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
      'email': email,
      'address': address,
    };
  }
}

// Common categories for Yellow Pages
class YellowPagesCategories {
  static const String advocate = 'Advocate';
  static const String lawFirm = 'Law Firm';
  static const String notary = 'Notary';
  static const String courtClerk = 'Court Clerk';
  static const String expertWitness = 'Expert Witness';
  static const String other = 'Other';

  static const List<String> allCategories = [
    advocate,
    lawFirm,
    notary,
    courtClerk,
    expertWitness,
    other,
  ];
}

// Common specialties for legal professionals
class LegalSpecialties {
  static const String criminalLaw = 'Criminal Law';
  static const String civilLaw = 'Civil Law';
  static const String familyLaw = 'Family Law';
  static const String corporateLaw = 'Corporate Law';
  static const String taxLaw = 'Tax Law';
  static const String propertyLaw = 'Property Law';
  static const String laborLaw = 'Labor Law';
  static const String intellectualPropertyLaw = 'Intellectual Property Law';
  static const String environmentalLaw = 'Environmental Law';
  static const String constitutionalLaw = 'Constitutional Law';

  static const List<String> allSpecialties = [
    criminalLaw,
    civilLaw,
    familyLaw,
    corporateLaw,
    taxLaw,
    propertyLaw,
    laborLaw,
    intellectualPropertyLaw,
    environmentalLaw,
    constitutionalLaw,
  ];
}
