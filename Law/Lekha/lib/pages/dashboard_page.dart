import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/language_service.dart';
import '../services/auth_service.dart';
import '../models/user.dart';
import '../models/case.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  DashboardPageState createState() => DashboardPageState();
}

class DashboardPageState extends State<DashboardPage> {
  @override
  Widget build(BuildContext context) {
    return Consumer2<LanguageService, AuthService>(
      builder: (context, languageService, authService, child) {
        final user = authService.currentUser;
        if (user == null) return const SizedBox();

        return Scaffold(
          backgroundColor: Color(0xFF0D0D0D),
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with user info
                  _buildHeader(user, languageService),
                  <PERSON><PERSON><PERSON>ox(height: 32),

                  // Quick stats
                  _buildQuickStats(user, languageService),
                  SizedBox(height: 32),

                  // Recent cases
                  _buildRecentCases(user, languageService),
                  SizedBox(height: 32),

                  // Upcoming hearings
                  _buildUpcomingHearings(user, languageService),
                  SizedBox(height: 32),

                  // Quick actions based on user type
                  _buildQuickActions(user, languageService),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(User user, LanguageService languageService) {
    return Row(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFFFF9933), Color(0xFFFF7722)],
            ),
            borderRadius: BorderRadius.circular(30),
          ),
          child: Center(
            child: Text(
              user.profile.name.firstName[0].toUpperCase(),
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${languageService.getText('welcome')} ${user.profile.name.firstName}',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                _getUserTypeText(user.userType, languageService),
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () {
            // Navigate to notifications
          },
          icon: Icon(
            Icons.notifications_outlined,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStats(User user, LanguageService languageService) {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageService.getText('quick_stats'),
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  '12',
                  languageService.getText('active_cases'),
                  Color(0xFF4ADE80),
                  Icons.gavel_rounded,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  '3',
                  languageService.getText('hearings_today'),
                  Color(0xFF60A5FA),
                  Icons.calendar_today_rounded,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  '8',
                  languageService.getText('pending_tasks'),
                  Color(0xFFFBBF24),
                  Icons.task_alt_rounded,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  '25',
                  languageService.getText('total_clients'),
                  Color(0xFFF87171),
                  Icons.people_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String value, String label, Color color, IconData icon) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                value,
                style: TextStyle(
                  color: color,
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                ),
              ),
              Icon(
                icon,
                color: color,
                size: 20,
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentCases(User user, LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              languageService.getText('recent_cases'),
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to all cases
              },
              child: Text(
                languageService.getText('view_all'),
                style: TextStyle(
                  color: Color(0xFFFF9933),
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 16),
        // Mock case data
        _buildCaseCard(
          'CW-2024-001',
          'Property Dispute Case',
          'Active',
          'Next hearing: Dec 15, 2024',
          Color(0xFF4ADE80),
        ),
        SizedBox(height: 12),
        _buildCaseCard(
          'CR-2024-045',
          'Criminal Defense',
          'Under Review',
          'Document pending',
          Color(0xFFFBBF24),
        ),
        SizedBox(height: 12),
        _buildCaseCard(
          'CV-2024-023',
          'Contract Violation',
          'Closed',
          'Settled on Dec 1, 2024',
          Color(0xFF6B7280),
        ),
      ],
    );
  }

  Widget _buildCaseCard(String caseNumber, String title, String status, String subtitle, Color statusColor) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                caseNumber,
                style: TextStyle(
                  color: Color(0xFFFF9933),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  status,
                  style: TextStyle(
                    color: statusColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingHearings(User user, LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.getText('upcoming_hearings'),
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16),
        _buildHearingCard(
          'Dec 15, 2024',
          '10:30 AM',
          'Property Dispute - CW-2024-001',
          'District Court, Bengaluru',
        ),
        SizedBox(height: 12),
        _buildHearingCard(
          'Dec 18, 2024',
          '2:00 PM',
          'Contract Review - CV-2024-023',
          'High Court, Karnataka',
        ),
      ],
    );
  }

  Widget _buildHearingCard(String date, String time, String title, String court) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 60,
            decoration: BoxDecoration(
              color: Color(0xFF60A5FA),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  '$date at $time',
                  style: TextStyle(
                    color: Color(0xFF60A5FA),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  court,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.6),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(User user, LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.getText('quick_actions'),
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: _getQuickActionsForUserType(user, languageService),
        ),
      ],
    );
  }

  List<Widget> _getQuickActionsForUserType(User user, LanguageService languageService) {
    List<Widget> actions = [];

    if (user.accessControl.canAddCases) {
      actions.add(_buildActionCard(
        languageService.getText('add_case'),
        Icons.add_circle_outline,
        Color(0xFF4ADE80),
        () {
          // Navigate to add case
        },
      ));
    }

    actions.addAll([
      _buildActionCard(
        languageService.getText('calendar'),
        Icons.calendar_today_outlined,
        Color(0xFF60A5FA),
        () {
          // Navigate to calendar
        },
      ),
      _buildActionCard(
        languageService.getText('documents'),
        Icons.description_outlined,
        Color(0xFFFBBF24),
        () {
          // Navigate to documents
        },
      ),
      _buildActionCard(
        languageService.getText('yellow_pages'),
        Icons.contacts_outlined,
        Color(0xFFF87171),
        () {
          // Navigate to yellow pages
        },
      ),
    ]);

    if (user.userType == UserType.intern) {
      actions.add(_buildActionCard(
        languageService.getText('job_sheets'),
        Icons.assignment_outlined,
        Color(0xFF8B5CF6),
        () {
          // Navigate to job sheets
        },
      ));
    }

    return actions;
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Color(0xFF1A1A1A),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _getUserTypeText(UserType userType, LanguageService languageService) {
    switch (userType) {
      case UserType.advocate:
        return languageService.getText('advocate');
      case UserType.jrAdvocate:
        return languageService.getText('jr_advocate');
      case UserType.intern:
        return languageService.getText('intern');
    }
  }
}
