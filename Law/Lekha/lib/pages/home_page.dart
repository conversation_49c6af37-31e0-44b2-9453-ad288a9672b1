import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/language_service.dart';
import '../services/auth_service.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  HomePageState createState() => HomePageState();
}

class HomePageState extends State<HomePage> with TickerProviderStateMixin {
  bool isListening = false;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.12).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void startListening() {
    setState(() {
      isListening = true;
    });
    _pulseController.repeat(reverse: true);

    Future.delayed(Duration(seconds: 3), () {
      stopListening();
    });
  }

  void stopListening() {
    setState(() {
      isListening = false;
    });
    _pulseController.stop();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final horizontalPadding = isSmallScreen ? 16.0 : 24.0;

    return Consumer2<LanguageService, AuthService>(
      builder: (context, languageService, authService, child) {
        return Scaffold(
          backgroundColor: Color(0xFF0D0D0D),
          body: SafeArea(
            child: SingleChildScrollView(
              physics: BouncingScrollPhysics(),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: isSmallScreen ? 16 : 24),

                    // Header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                languageService.getText('good_morning'),
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.6),
                                  fontSize: isSmallScreen ? 14 : 16,
                                  fontWeight: FontWeight.w400,
                                  letterSpacing: 0.2,
                                ),
                              ),
                              SizedBox(height: 4),
                              Wrap(
                                children: [
                                  Text(
                                    languageService.getText('app_name'),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: isSmallScreen ? 24 : 32,
                                      fontWeight: FontWeight.w300,
                                      letterSpacing: -1,
                                      fontFamily: 'Noto Sans Devanagari',
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    languageService.getText(
                                      'app_name',
                                      useEnglish: true,
                                    ),
                                    style: TextStyle(
                                      color: Colors.white.withValues(
                                        alpha: 0.7,
                                      ),
                                      fontSize: isSmallScreen ? 14 : 18,
                                      fontWeight: FontWeight.w300,
                                      letterSpacing: -0.5,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.all(isSmallScreen ? 10 : 14),
                          decoration: BoxDecoration(
                            color: Color(0xFF1A1A1A),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.08),
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            Icons.notifications_none_rounded,
                            color: Colors.white.withValues(alpha: 0.7),
                            size: isSmallScreen ? 18 : 22,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: isSmallScreen ? 32 : 48),

                    // Main Hero Section
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(isSmallScreen ? 20 : 28),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(
                          isSmallScreen ? 20 : 28,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 40,
                            offset: Offset(0, 20),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(isSmallScreen ? 8 : 10),
                                decoration: BoxDecoration(
                                  color: Color(0xFF0D0D0D),
                                  borderRadius: BorderRadius.circular(14),
                                ),
                                child: Icon(
                                  Icons.gavel_rounded,
                                  color: Colors.white,
                                  size: isSmallScreen ? 16 : 20,
                                ),
                              ),
                              SizedBox(width: isSmallScreen ? 10 : 14),
                              Flexible(
                                child: Text(
                                  'Legal Command Center',
                                  style: TextStyle(
                                    color: Color(0xFF0D0D0D),
                                    fontSize: isSmallScreen ? 16 : 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: isSmallScreen ? 16 : 20),
                          Text(
                            'Streamlined case management,\nclient relations, and court\nproceedings intelligence.',
                            style: TextStyle(
                              color: Color(0xFF0D0D0D).withValues(alpha: 0.7),
                              fontSize: isSmallScreen ? 13 : 15,
                              fontWeight: FontWeight.w400,
                              height: 1.5,
                              letterSpacing: 0.1,
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: isSmallScreen ? 32 : 40),

                    // Services Grid
                    Text(
                      'Services',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: isSmallScreen ? 20 : 24,
                        fontWeight: FontWeight.w500,
                        letterSpacing: -0.5,
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 16 : 24),

                    LayoutBuilder(
                      builder: (context, constraints) {
                        return GridView.count(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          crossAxisCount: 2,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                          childAspectRatio: isSmallScreen ? 1.0 : 1.15,
                          children: [
                            _buildServiceCard(
                              'Case Tracker',
                              Icons.track_changes_outlined,
                              Color(0xFF4ADE80),
                            ),
                            _buildServiceCard(
                              'Client Portal',
                              Icons.people_outline_rounded,
                              Color(0xFF60A5FA),
                            ),
                            _buildServiceCard(
                              'Documents',
                              Icons.description_outlined,
                              Color(0xFFFBBF24),
                            ),
                            _buildServiceCard(
                              'Analytics',
                              Icons.analytics_outlined,
                              Color(0xFFF87171),
                            ),
                          ],
                        );
                      },
                    ),

                    SizedBox(height: 40),

                    // Activity Feed
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Activity',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.w500,
                            letterSpacing: -0.5,
                          ),
                        ),
                        Text(
                          'view all',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.6),
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 20),

                    _buildActivityItem(
                      'Case status updated',
                      'CW-45234 • 2h ago',
                      Icons.fiber_manual_record,
                      Color(0xFF4ADE80),
                    ),
                    _buildActivityItem(
                      'Client meeting scheduled',
                      'Consultation • 4h ago',
                      Icons.fiber_manual_record,
                      Color(0xFF60A5FA),
                    ),
                    _buildActivityItem(
                      'Document processed',
                      'WP-7890 • 1d ago',
                      Icons.fiber_manual_record,
                      Color(0xFFFBBF24),
                    ),

                    SizedBox(height: 120),
                  ],
                ),
              ),
            ),
          ),

          // Premium Voice Button
          floatingActionButton: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: isListening ? _pulseAnimation.value : 1.0,
                child: Container(
                  width: 72,
                  height: 72,
                  decoration: BoxDecoration(
                    color: isListening ? Color(0xFFEF4444) : Colors.white,
                    borderRadius: BorderRadius.circular(36),
                    boxShadow: [
                      BoxShadow(
                        color: (isListening ? Color(0xFFEF4444) : Colors.white)
                            .withValues(alpha: 0.3),
                        blurRadius: 24,
                        offset: Offset(0, 12),
                      ),
                    ],
                  ),
                  child: FloatingActionButton(
                    onPressed: isListening ? stopListening : startListening,
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    child: Icon(
                      isListening ? Icons.stop_rounded : Icons.mic_rounded,
                      color: isListening ? Colors.white : Color(0xFF0D0D0D),
                      size: 32,
                    ),
                  ),
                ),
              );
            },
          ),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
        );
      },
    );
  }

  Widget _buildServiceCard(String title, IconData icon, Color color) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
      decoration: BoxDecoration(
        color: Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 10 : 14),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(icon, color: color, size: isSmallScreen ? 20 : 26),
          ),
          SizedBox(height: isSmallScreen ? 12 : 16),
          Text(
            title,
            style: TextStyle(
              fontSize: isSmallScreen ? 12 : 14,
              fontWeight: FontWeight.w500,
              color: Colors.white,
              letterSpacing: 0.1,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.06),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(2),
            child: Icon(icon, color: color, size: 12),
          ),
          SizedBox(width: 18),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                    letterSpacing: 0.1,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.white.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
