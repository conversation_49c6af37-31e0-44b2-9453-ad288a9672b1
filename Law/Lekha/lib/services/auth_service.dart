import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import 'storage_service.dart';

class AuthService extends ChangeNotifier {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  User? _currentUser;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  bool _googleSignInAvailable = true;

  User? get currentUser => _currentUser;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  bool get googleSignInAvailable => _googleSignInAvailable;

  GoogleSignIn? _googleSignIn;

  final StorageService _storageService = StorageService();

  Future<void> initialize() async {
    _setLoading(true);
    try {
      // Initialize Google Sign-In with error handling for web
      try {
        _googleSignIn = GoogleSignIn(
          scopes: [
            'email',
            'profile',
            'https://www.googleapis.com/auth/calendar',
          ],
        );
        // Test if Google Sign-In is available (especially for web)
        if (kIsWeb) {
          // On web, we need to be more careful about Google Sign-In initialization
          _googleSignInAvailable =
              true; // We'll handle errors in the sign-in method
        }
      } catch (e) {
        debugPrint('Google Sign-In not available: $e');
        _googleSignInAvailable = false;
        _googleSignIn = null;
      }

      // Check if user is already logged in
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('current_user_id');

      if (userId != null) {
        // Load user from local storage
        final userData = await _storageService.getUser(userId);
        if (userData != null) {
          _currentUser = userData;
          _isAuthenticated = true;
        }
      }
    } catch (e) {
      debugPrint('Error initializing auth service: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signInWithEmail(String email, String password) async {
    _setLoading(true);
    try {
      // TODO: Implement actual email/password authentication
      // For now, create a mock user
      final user = await _createMockUser(email, UserType.advocate);
      await _setCurrentUser(user);
      return true;
    } catch (e) {
      debugPrint('Error signing in with email: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signInWithDummyCredentials() async {
    _setLoading(true);
    try {
      // Create a demo user with predefined credentials
      final user = await _createMockUser('<EMAIL>', UserType.advocate);
      await _setCurrentUser(user);
      return true;
    } catch (e) {
      debugPrint('Error signing in with dummy credentials: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signInWithGoogle() async {
    if (!_googleSignInAvailable || _googleSignIn == null) {
      debugPrint('Google Sign-In not available');
      return false;
    }

    _setLoading(true);
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn!.signIn();
      if (googleUser == null) {
        return false; // User cancelled
      }

      // Get additional user info
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Check if user exists in local storage
      final existingUser = await _storageService.getUserByEmail(
        googleUser.email,
      );

      if (existingUser != null) {
        // Update existing user with latest Google data
        final updatedUser = await _updateUserWithGoogleData(
          existingUser,
          googleUser,
        );
        await _setCurrentUser(updatedUser);
        return true;
      } else {
        // New user - create user with Google data
        final user = await _createUserFromGoogleAccount(googleUser);
        await _setCurrentUser(user);
        return true;
      }
    } catch (e) {
      debugPrint('Error signing in with Google: $e');
      // If Google Sign-In fails, mark it as unavailable
      _googleSignInAvailable = false;
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> createUser({
    required String email,
    String? password,
    required UserType userType,
    required UserProfile profile,
    required String languagePreference,
    GoogleSignInAccount? googleAccount,
  }) async {
    _setLoading(true);
    try {
      User user;

      if (googleAccount != null) {
        // Create user from Google account data
        user = await _createUserFromGoogleAccount(
          googleAccount,
          userType: userType,
          languagePreference: languagePreference,
          customProfile: profile,
        );
      } else {
        // Create regular user
        user = User(
          userId: DateTime.now().millisecondsSinceEpoch.toString(),
          email: email,
          passwordHash: password != null ? _hashPassword(password) : null,
          userType: userType,
          languagePreference: languagePreference,
          createdAt: DateTime.now(),
          profile: profile,
          accessControl: _getAccessControlForUserType(userType),
        );
      }

      await _storageService.saveUser(user);
      await _setCurrentUser(user);
      return true;
    } catch (e) {
      debugPrint('Error creating user: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    _setLoading(true);
    try {
      // Only sign out from Google if it's available
      if (_googleSignIn != null) {
        await _googleSignIn!.signOut();
      }
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user_id');

      _currentUser = null;
      _isAuthenticated = false;
    } catch (e) {
      debugPrint('Error signing out: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateUserProfile(UserProfile profile) async {
    if (_currentUser == null) return false;

    try {
      final updatedUser = User(
        userId: _currentUser!.userId,
        email: _currentUser!.email,
        passwordHash: _currentUser!.passwordHash,
        userType: _currentUser!.userType,
        languagePreference: _currentUser!.languagePreference,
        createdAt: _currentUser!.createdAt,
        lastLogin: DateTime.now(),
        profile: profile,
        accessControl: _currentUser!.accessControl,
      );

      await _storageService.saveUser(updatedUser);
      _currentUser = updatedUser;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      return false;
    }
  }

  Future<bool> updateLanguagePreference(String languageCode) async {
    if (_currentUser == null) return false;

    try {
      final updatedUser = User(
        userId: _currentUser!.userId,
        email: _currentUser!.email,
        passwordHash: _currentUser!.passwordHash,
        userType: _currentUser!.userType,
        languagePreference: languageCode,
        createdAt: _currentUser!.createdAt,
        lastLogin: DateTime.now(),
        profile: _currentUser!.profile,
        accessControl: _currentUser!.accessControl,
      );

      await _storageService.saveUser(updatedUser);
      _currentUser = updatedUser;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error updating language preference: $e');
      return false;
    }
  }

  // NEW METHOD: Create user from Google account data
  Future<User> _createUserFromGoogleAccount(
    GoogleSignInAccount googleAccount, {
    UserType? userType,
    String? languagePreference,
    UserProfile? customProfile,
  }) async {
    // Parse name from Google display name
    final nameParts = (googleAccount.displayName ?? '').split(' ');
    final firstName = nameParts.isNotEmpty ? nameParts.first : '';
    final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

    final profile =
        customProfile ??
        UserProfile(
          name: Name(
            firstName: firstName.isNotEmpty ? firstName : 'User',
            lastName: lastName,
          ),
          contact: Contact(
            email: googleAccount.email,
            mobile: '', // Google doesn't provide phone number in basic scope
          ),
          address: Address(street: '', city: '', state: '', zipCode: ''),
          // Set default values based on user type or leave null for completion later
          advocateLicenseNumber: userType != UserType.intern ? null : null,
          officeName: null,
          numberOfEmployees: null,
          registeredCompanyType: null,
          roleAtCompany: null,
          advocateStatus: null,
          age: null,
          college: null,
          licensesEnrolledStatus: userType == UserType.intern
              ? LicenseStatus.student
              : null,
          internRequests: userType == UserType.intern ? [] : null,
        );

    return User(
      userId: DateTime.now().millisecondsSinceEpoch.toString(),
      email: googleAccount.email,
      userType:
          userType ?? UserType.advocate, // Default to advocate if not specified
      languagePreference: languagePreference ?? 'english', // Default to English
      createdAt: DateTime.now(),
      lastLogin: DateTime.now(),
      profile: profile,
      accessControl: _getAccessControlForUserType(
        userType ?? UserType.advocate,
      ),
    );
  }

  // NEW METHOD: Update existing user with Google data
  Future<User> _updateUserWithGoogleData(
    User existingUser,
    GoogleSignInAccount googleAccount,
  ) async {
    // Update last login and Google ID if not set
    return User(
      userId: existingUser.userId,
      email: existingUser.email,
      passwordHash: existingUser.passwordHash,
      userType: existingUser.userType,
      languagePreference: existingUser.languagePreference,
      createdAt: existingUser.createdAt,
      lastLogin: DateTime.now(),
      profile: existingUser.profile,
      accessControl: existingUser.accessControl,
    );
  }

  Future<void> _setCurrentUser(User user) async {
    _currentUser = user;
    _isAuthenticated = true;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('current_user_id', user.userId);

    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  Future<User> _createMockUser(String email, UserType userType) async {
    return User(
      userId: DateTime.now().millisecondsSinceEpoch.toString(),
      email: email,
      userType: userType,
      languagePreference: 'hindi',
      createdAt: DateTime.now(),
      profile: UserProfile(
        name: Name(firstName: 'John', lastName: 'Doe'),
        contact: Contact(email: email, mobile: '+919876543210'),
        address: Address(
          street: '123 Main St',
          city: 'Bengaluru',
          state: 'Karnataka',
          zipCode: '560001',
        ),
        advocateLicenseNumber: userType != UserType.intern ? 'ADV123456' : null,
        officeName: userType != UserType.intern
            ? 'Legal Eagles Law Firm'
            : null,
        numberOfEmployees: userType == UserType.advocate ? 5 : null,
        registeredCompanyType: userType == UserType.advocate
            ? CompanyType.partnership
            : null,
        roleAtCompany: userType != UserType.intern ? 'Senior Partner' : null,
        advocateStatus: userType != UserType.intern
            ? AdvocateStatus.active
            : null,
        age: userType == UserType.intern ? 22 : null,
        college: userType == UserType.intern
            ? 'National Law School of India University'
            : null,
        licensesEnrolledStatus: userType == UserType.intern
            ? LicenseStatus.student
            : null,
        internRequests: userType == UserType.intern ? [] : null,
      ),
      accessControl: _getAccessControlForUserType(userType),
    );
  }

  AccessControl _getAccessControlForUserType(UserType userType) {
    switch (userType) {
      case UserType.advocate:
        return AccessControl.forAdvocate();
      case UserType.jrAdvocate:
        return AccessControl.forJrAdvocate();
      case UserType.intern:
        return AccessControl.forIntern();
    }
  }

  String _hashPassword(String password) {
    // TODO: Implement proper password hashing
    return password; // Placeholder
  }
}
