import 'package:flutter/material.dart';

class LanguageService extends ChangeNotifier {
  static final LanguageService _instance = LanguageService._internal();
  factory LanguageService() => _instance;
  LanguageService._internal();

  String _selectedLanguage = 'hindi';

  String get selectedLanguage => _selectedLanguage;

  void setLanguage(String language) {
    _selectedLanguage = language;
    notifyListeners();
  }

  // Language options
  static const Map<String, Map<String, String>> languages = {
    'hindi': {'name': 'हिंदी', 'englishName': 'Hindi', 'code': 'hi'},
    'tamil': {'name': 'தமிழ்', 'englishName': 'Tamil', 'code': 'ta'},
    'telugu': {'name': 'తెలుగు', 'englishName': 'Telugu', 'code': 'te'},
    'kannada': {'name': 'ಕನ್ನಡ', 'englishName': 'Kannada', 'code': 'kn'},
    'malayalam': {'name': 'മലയാളം', 'englishName': 'Malayalam', 'code': 'ml'},
    'gujarati': {'name': 'ગુજરાતી', 'englishName': 'Gujarati', 'code': 'gu'},
    'marathi': {'name': 'मराठी', 'englishName': 'Marathi', 'code': 'mr'},
    'bengali': {'name': 'বাংলা', 'englishName': 'Bengali', 'code': 'bn'},
    'punjabi': {'name': 'ਪੰਜਾਬੀ', 'englishName': 'Punjabi', 'code': 'pa'},
    'odia': {'name': 'ଓଡ଼ିଆ', 'englishName': 'Odia', 'code': 'or'},
  };

  // Translations
  static const Map<String, Map<String, String>> translations = {
    // App name and welcome
    'app_name': {
      'hindi': 'लेखा',
      'tamil': 'லேகா',
      'telugu': 'లేఖా',
      'kannada': 'ಲೇಖಾ',
      'malayalam': 'ലേഖാ',
      'gujarati': 'લેખા',
      'marathi': 'लेखा',
      'bengali': 'লেখা',
      'punjabi': 'ਲੇਖਾ',
      'odia': 'ଲେଖା',
      'english': 'LEKHA',
    },
    'welcome': {
      'hindi': 'आपका स्वागत है',
      'tamil': 'வரவேற்கிறோம்',
      'telugu': 'స్వాగతం',
      'kannada': 'ಸ್ವಾಗತ',
      'malayalam': 'സ്വാഗതം',
      'gujarati': 'સ્વાગત છે',
      'marathi': 'स्वागत आहे',
      'bengali': 'স্বাগতম',
      'punjabi': 'ਸਵਾਗਤ ਹੈ',
      'odia': 'ସ୍ୱାଗତ',
      'english': 'Welcome',
    },
    'login': {
      'hindi': 'प्रवेश करें',
      'tamil': 'உள்நுழைய',
      'telugu': 'లాగిన్',
      'kannada': 'ಲಾಗಿನ್',
      'malayalam': 'ലോഗിൻ',
      'gujarati': 'લોગિન',
      'marathi': 'लॉगिन',
      'bengali': 'লগইন',
      'punjabi': 'ਲਾਗਇਨ',
      'odia': 'ଲଗଇନ',
      'english': 'Sign In',
    },
    'email': {
      'hindi': 'ईमेल पता',
      'tamil': 'மின்னஞ்சல்',
      'telugu': 'ఇమెయిల్',
      'kannada': 'ಇಮೇಲ್',
      'malayalam': 'ഇമെയിൽ',
      'gujarati': 'ઇમેઇલ',
      'marathi': 'ईमेल',
      'bengali': 'ইমেইল',
      'punjabi': 'ਈਮੇਲ',
      'odia': 'ଇମେଲ',
      'english': 'Email',
    },
    'password': {
      'hindi': 'पासवर्ड',
      'tamil': 'கடவுச்சொல்',
      'telugu': 'పాస్‌వర్డ్',
      'kannada': 'ಪಾಸ್‌ವರ್ಡ್',
      'malayalam': 'പാസ്‌വേഡ്',
      'gujarati': 'પાસવર્ડ',
      'marathi': 'पासवर्ड',
      'bengali': 'পাসওয়ার্ড',
      'punjabi': 'ਪਾਸਵਰਡ',
      'odia': 'ପାସୱାର୍ଡ',
      'english': 'Password',
    },
    'home': {
      'hindi': 'होम',
      'tamil': 'முகப்பு',
      'telugu': 'హోమ్',
      'kannada': 'ಮುಖಪುಟ',
      'malayalam': 'ഹോം',
      'gujarati': 'હોમ',
      'marathi': 'होम',
      'bengali': 'হোম',
      'punjabi': 'ਘਰ',
      'odia': 'ଘର',
      'english': 'Home',
    },
    'calendar': {
      'hindi': 'कैलेंडर',
      'tamil': 'நாட்காட்டி',
      'telugu': 'క్యాలెండర్',
      'kannada': 'ಕ್ಯಾಲೆಂಡರ್',
      'malayalam': 'കലണ്ടർ',
      'gujarati': 'કેલેન્ડર',
      'marathi': 'कैलेंडर',
      'bengali': 'ক্যালেন্ডার',
      'punjabi': 'ਕੈਲੰਡਰ',
      'odia': 'କ୍ୟାଲେଣ୍ଡର',
      'english': 'Calendar',
    },
    'tasks': {
      'hindi': 'कार्य प्रबंधन',
      'tamil': 'பணி மேலாண்மை',
      'telugu': 'కార్య నిర్వహణ',
      'kannada': 'ಕಾರ್ಯ ನಿರ್ವಹಣೆ',
      'malayalam': 'ടാസ്ക് മാനേജ്മെന്റ്',
      'gujarati': 'કાર્ય વ્યવસ્થાપન',
      'marathi': 'कार्य व्यवस्थापन',
      'bengali': 'কাজ ব্যবস্থাপনা',
      'punjabi': 'ਕੰਮ ਪ੍ਰਬੰਧਨ',
      'odia': 'କାର୍ଯ୍ୟ ପରିଚାଳନା',
      'english': 'Task Management',
    },
    'profile': {
      'hindi': 'प्रोफ़ाइल',
      'tamil': 'சுயவிவரம்',
      'telugu': 'ప్రొఫైల్',
      'kannada': 'ಪ್ರೊಫೈಲ್',
      'malayalam': 'പ്രൊഫൈൽ',
      'gujarati': 'પ્રોફાઇલ',
      'marathi': 'प्रोफाइल',
      'bengali': 'প্রোফাইল',
      'punjabi': 'ਪ੍ਰੋਫਾਈਲ',
      'odia': 'ପ୍ରୋଫାଇଲ',
      'english': 'Profile',
    },
    'good_morning': {
      'hindi': 'सुप्रभात',
      'tamil': 'காலை வணக்கம்',
      'telugu': 'శుభోదయం',
      'kannada': 'ಶುಭೋದಯ',
      'malayalam': 'സുപ്രഭാതം',
      'gujarati': 'સુપ્રભાત',
      'marathi': 'सुप्रभात',
      'bengali': 'সুপ্রভাত',
      'punjabi': 'ਸਤ ਸ੍ਰੀ ਅਕਾਲ',
      'odia': 'ସୁପ୍ରଭାତ',
      'english': 'good morning',
    },
    'choose_language': {
      'hindi': 'भाषा चुनें',
      'tamil': 'மொழியைத் தேர்ந்தெடுக்கவும்',
      'telugu': 'భాషను ఎంచుకోండి',
      'kannada': 'ಭಾಷೆಯನ್ನು ಆಯ್ಕೆಮಾಡಿ',
      'malayalam': 'ഭാഷ തിരഞ്ഞെടുക്കുക',
      'gujarati': 'ભાષા પસંદ કરો',
      'marathi': 'भाषा निवडा',
      'bengali': 'ভাষা নির্বাচন করুন',
      'punjabi': 'ਭਾਸ਼ਾ ਚੁਣੋ',
      'odia': 'ଭାଷା ବାଛନ୍ତୁ',
      'english': 'Choose Language',
    },
    'advocate': {
      'hindi': 'अधिवक्ता',
      'tamil': 'வழக்கறிஞர்',
      'telugu': 'న్యాయవాది',
      'kannada': 'ವಕೀಲ',
      'malayalam': 'അഭിഭാഷകൻ',
      'gujarati': 'વકીલ',
      'marathi': 'वकील',
      'bengali': 'আইনজীবী',
      'punjabi': 'ਵਕੀਲ',
      'odia': 'ଓକିଲ',
      'english': 'Advocate',
    },
    'jr_advocate': {
      'hindi': 'जूनियर अधिवक्ता',
      'tamil': 'இளைய வழக்கறிஞர்',
      'telugu': 'జూనియర్ న్యాయవాది',
      'kannada': 'ಜೂನಿಯರ್ ವಕೀಲ',
      'malayalam': 'ജൂനിയർ അഭിഭാഷകൻ',
      'gujarati': 'જુનિયર વકીલ',
      'marathi': 'कनिष्ठ वकील',
      'bengali': 'জুনিয়র আইনজীবী',
      'punjabi': 'ਜੂਨੀਅਰ ਵਕੀਲ',
      'odia': 'ଜୁନିଅର ଓକିଲ',
      'english': 'Jr. Advocate',
    },
    'intern': {
      'hindi': 'इंटर्न',
      'tamil': 'பயிற்சியாளர்',
      'telugu': 'ఇంటర్న్',
      'kannada': 'ಇಂಟರ್ನ್',
      'malayalam': 'ഇന്റേൺ',
      'gujarati': 'ઇન્ટર્ન',
      'marathi': 'इंटर्न',
      'bengali': 'ইন্টার্ন',
      'punjabi': 'ਇੰਟਰਨ',
      'odia': 'ଇଣ୍ଟର୍ନ',
      'english': 'Intern',
    },
    'quick_stats': {
      'hindi': 'त्वरित आंकड़े',
      'tamil': 'விரைவு புள்ளிவிவரங்கள்',
      'telugu': 'త్వరిత గణాంకాలు',
      'kannada': 'ತ್ವರಿತ ಅಂಕಿಅಂಶಗಳು',
      'malayalam': 'ദ്രുത സ്ഥിതിവിവരക്കണക്കുകൾ',
      'gujarati': 'ઝડપી આંકડા',
      'marathi': 'त्वरित आकडेवारी',
      'bengali': 'দ্রুত পরিসংখ্যান',
      'punjabi': 'ਤੇਜ਼ ਅੰਕੜੇ',
      'odia': 'ଦ୍ରୁତ ପରିସଂଖ୍ୟାନ',
      'english': 'Quick Stats',
    },
    'active_cases': {
      'hindi': 'सक्रिय मामले',
      'tamil': 'செயலில் உள்ள வழக்குகள்',
      'telugu': 'క్రియాశీల కేసులు',
      'kannada': 'ಸಕ್ರಿಯ ಪ್ರಕರಣಗಳು',
      'malayalam': 'സജീവ കേസുകൾ',
      'gujarati': 'સક્રિય કેસો',
      'marathi': 'सक्रिय प्रकरणे',
      'bengali': 'সক্রিয় মামলা',
      'punjabi': 'ਸਰਗਰਮ ਕੇਸ',
      'odia': 'ସକ୍ରିୟ ମାମଲା',
      'english': 'Active Cases',
    },
    'hearings_today': {
      'hindi': 'आज की सुनवाई',
      'tamil': 'இன்றைய விசாரணைகள்',
      'telugu': 'నేటి విచారణలు',
      'kannada': 'ಇಂದಿನ ವಿಚಾರಣೆಗಳು',
      'malayalam': 'ഇന്നത്തെ വിചാരണകൾ',
      'gujarati': 'આજની સુનાવણી',
      'marathi': 'आजची सुनावणी',
      'bengali': 'আজকের শুনানি',
      'punjabi': 'ਅੱਜ ਦੀ ਸੁਣਵਾਈ',
      'odia': 'ଆଜିର ଶୁଣାଣି',
      'english': 'Hearings Today',
    },
    'pending_tasks': {
      'hindi': 'लंबित कार्य',
      'tamil': 'நிலுவையில் உள்ள பணிகள்',
      'telugu': 'పెండింగ్ టాస్క్‌లు',
      'kannada': 'ಬಾಕಿ ಇರುವ ಕಾರ್ಯಗಳು',
      'malayalam': 'തീർപ്പാക്കാത്ത ജോലികൾ',
      'gujarati': 'બાકી કાર્યો',
      'marathi': 'प्रलंबित कार्ये',
      'bengali': 'অমীমাংসিত কাজ',
      'punjabi': 'ਬਾਕੀ ਕੰਮ',
      'odia': 'ବିଚାରାଧୀନ କାର୍ଯ୍ୟ',
      'english': 'Pending Tasks',
    },
    'total_clients': {
      'hindi': 'कुल ग्राहक',
      'tamil': 'மொத்த வாடிக்கையாளர்கள்',
      'telugu': 'మొత్తం క్లయింట్లు',
      'kannada': 'ಒಟ್ಟು ಗ್ರಾಹಕರು',
      'malayalam': 'മൊത്തം ക്ലയന്റുകൾ',
      'gujarati': 'કુલ ક્લાયન્ટ્સ',
      'marathi': 'एकूण क्लायंट',
      'bengali': 'মোট ক্লায়েন্ট',
      'punjabi': 'ਕੁੱਲ ਕਲਾਇੰਟ',
      'odia': 'ମୋଟ କ୍ଲାଏଣ୍ଟ',
      'english': 'Total Clients',
    },
    'recent_cases': {
      'hindi': 'हाल के मामले',
      'tamil': 'சமீபத்திய வழக்குகள்',
      'telugu': 'ఇటీవలి కేసులు',
      'kannada': 'ಇತ್ತೀಚಿನ ಪ್ರಕರಣಗಳು',
      'malayalam': 'സമീപകാല കേസുകൾ',
      'gujarati': 'તાજેતરના કેસો',
      'marathi': 'अलीकडील प्रकरणे',
      'bengali': 'সাম্প্রতিক মামলা',
      'punjabi': 'ਹਾਲ ਦੇ ਕੇਸ',
      'odia': 'ସାମ୍ପ୍ରତିକ ମାମଲା',
      'english': 'Recent Cases',
    },
    'view_all': {
      'hindi': 'सभी देखें',
      'tamil': 'அனைத்தையும் பார்க்கவும்',
      'telugu': 'అన్నీ చూడండి',
      'kannada': 'ಎಲ್ಲವನ್ನೂ ನೋಡಿ',
      'malayalam': 'എല്ലാം കാണുക',
      'gujarati': 'બધું જુઓ',
      'marathi': 'सर्व पहा',
      'bengali': 'সব দেখুন',
      'punjabi': 'ਸਭ ਵੇਖੋ',
      'odia': 'ସବୁ ଦେଖନ୍ତୁ',
      'english': 'View All',
    },
    'upcoming_hearings': {
      'hindi': 'आगामी सुनवाई',
      'tamil': 'வரவிருக்கும் விசாரணைகள்',
      'telugu': 'రాబోయే విచారణలు',
      'kannada': 'ಮುಂಬರುವ ವಿಚಾರಣೆಗಳು',
      'malayalam': 'വരാനിരിക്കുന്ന വിചാരണകൾ',
      'gujarati': 'આગામી સુનાવણી',
      'marathi': 'येणाऱ्या सुनावणी',
      'bengali': 'আসন্ন শুনানি',
      'punjabi': 'ਆਉਣ ਵਾਲੀ ਸੁਣਵਾਈ',
      'odia': 'ଆଗାମୀ ଶୁଣାଣି',
      'english': 'Upcoming Hearings',
    },
    'quick_actions': {
      'hindi': 'त्वरित कार्य',
      'tamil': 'விரைவு செயல்கள்',
      'telugu': 'త్వరిత చర్యలు',
      'kannada': 'ತ್ವರಿತ ಕ್ರಿಯೆಗಳು',
      'malayalam': 'ദ്രുത പ്രവർത്തനങ്ങൾ',
      'gujarati': 'ઝડપી ક્રિયાઓ',
      'marathi': 'त्वरित क्रिया',
      'bengali': 'দ্রুত কর্ম',
      'punjabi': 'ਤੇਜ਼ ਕਾਰਵਾਈ',
      'odia': 'ଦ୍ରୁତ କାର୍ଯ୍ୟ',
      'english': 'Quick Actions',
    },
    'add_case': {
      'hindi': 'मामला जोड़ें',
      'tamil': 'வழக்கு சேர்க்கவும்',
      'telugu': 'కేసు జోడించండి',
      'kannada': 'ಪ್ರಕರಣ ಸೇರಿಸಿ',
      'malayalam': 'കേസ് ചേർക്കുക',
      'gujarati': 'કેસ ઉમેરો',
      'marathi': 'प्रकरण जोडा',
      'bengali': 'মামলা যোগ করুন',
      'punjabi': 'ਕੇਸ ਸ਼ਾਮਲ ਕਰੋ',
      'odia': 'ମାମଲା ଯୋଗ କରନ୍ତୁ',
      'english': 'Add Case',
    },
    'documents': {
      'hindi': 'दस्तावेज़',
      'tamil': 'ஆவணங்கள்',
      'telugu': 'పత్రాలు',
      'kannada': 'ದಾಖಲೆಗಳು',
      'malayalam': 'രേഖകൾ',
      'gujarati': 'દસ્તાવેજો',
      'marathi': 'कागदपत्रे',
      'bengali': 'নথিপত্র',
      'punjabi': 'ਦਸਤਾਵੇਜ਼',
      'odia': 'ଦଲିଲ',
      'english': 'Documents',
    },
    'yellow_pages': {
      'hindi': 'येलो पेजेस',
      'tamil': 'மஞ்சள் பக்கங்கள்',
      'telugu': 'ఎల్లో పేజీలు',
      'kannada': 'ಹಳದಿ ಪುಟಗಳು',
      'malayalam': 'മഞ്ഞ പേജുകൾ',
      'gujarati': 'પીળા પાના',
      'marathi': 'पिवळी पाने',
      'bengali': 'হলুদ পাতা',
      'punjabi': 'ਪੀਲੇ ਪੰਨੇ',
      'odia': 'ହଳଦିଆ ପୃଷ୍ଠା',
      'english': 'Yellow Pages',
    },
    'job_sheets': {
      'hindi': 'जॉब शीट्स',
      'tamil': 'வேலை தாள்கள்',
      'telugu': 'జాబ్ షీట్లు',
      'kannada': 'ಜಾಬ್ ಶೀಟ್‌ಗಳು',
      'malayalam': 'ജോബ് ഷീറ്റുകൾ',
      'gujarati': 'જોબ શીટ્સ',
      'marathi': 'जॉब शीट्स',
      'bengali': 'জব শিট',
      'punjabi': 'ਜੌਬ ਸ਼ੀਟਾਂ',
      'odia': 'ଜବ ସିଟ',
      'english': 'Job Sheets',
    },
  };

  String getText(String key, {bool useEnglish = false}) {
    if (useEnglish) {
      return translations[key]?['english'] ?? key;
    }
    return translations[key]?[_selectedLanguage] ??
        translations[key]?['english'] ??
        key;
  }

  String getLanguageName(String languageCode) {
    return languages[languageCode]?['name'] ?? languageCode;
  }

  String getLanguageEnglishName(String languageCode) {
    return languages[languageCode]?['englishName'] ?? languageCode;
  }
}
