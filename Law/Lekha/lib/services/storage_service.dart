import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../models/case.dart';
import '../models/diary_entry.dart';
import '../models/job_sheet.dart';
import '../models/yellow_pages_entry.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  // In-memory storage for web compatibility
  final Map<String, User> _users = {};

  // User operations
  Future<void> saveUser(User user) async {
    debugPrint('Saving user: ${user.userId} - ${user.email}');
    _users[user.userId] = user;
  }

  Future<User?> getUser(String userId) async {
    debugPrint('Getting user: $userId');
    return _users[userId];
  }

  Future<User?> getUserByEmail(String email) async {
    debugPrint('Getting user by email: $email');
    try {
      return _users.values.firstWhere((user) => user.email == email);
    } catch (e) {
      return null;
    }
  }

  // Placeholder methods for other operations (not implemented for demo)
  Future<void> saveCase(Case caseData) async {
    // Not implemented for demo
  }

  Future<List<Case>> getCasesForUser(String userId) async {
    // Not implemented for demo
    return [];
  }

  Future<Case?> getCase(String caseId) async {
    // Not implemented for demo
    return null;
  }

  Future<void> saveDiaryEntry(DiaryEntry entry) async {
    // Not implemented for demo
  }

  Future<List<DiaryEntry>> getDiaryEntriesForUser(String userId) async {
    // Not implemented for demo
    return [];
  }

  Future<void> saveJobSheet(JobSheet jobSheet) async {
    // Not implemented for demo
  }

  Future<List<JobSheet>> getJobSheetsForIntern(String internId) async {
    // Not implemented for demo
    return [];
  }

  Future<void> saveYellowPagesEntry(YellowPagesEntry entry) async {
    // Not implemented for demo
  }

  Future<List<YellowPagesEntry>> getAllYellowPagesEntries() async {
    // Not implemented for demo
    return [];
  }
}
