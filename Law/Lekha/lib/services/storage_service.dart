import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/user.dart';
import '../models/case.dart';
import '../models/diary_entry.dart';
import '../models/job_sheet.dart';
import '../models/yellow_pages_entry.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'lekha.db');
    return await openDatabase(path, version: 1, onCreate: _onCreate);
  }

  Future<void> _onCreate(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users(
        userId TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        passwordHash TEXT,
        userType TEXT NOT NULL,
        languagePreference TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        lastLogin TEXT,
        profileData TEXT NOT NULL,
        accessControlData TEXT NOT NULL
      )
    ''');

    // Cases table
    await db.execute('''
      CREATE TABLE cases(
        caseId TEXT PRIMARY KEY,
        caseType TEXT NOT NULL,
        status TEXT NOT NULL,
        filingData TEXT NOT NULL,
        registrationData TEXT NOT NULL,
        crnNumber TEXT,
        uniqueId TEXT,
        assignedUsersData TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        clientDetailsData TEXT NOT NULL,
        caseResearchFindingsData TEXT NOT NULL,
        hearingsData TEXT NOT NULL,
        caseHistoryData TEXT NOT NULL,
        aiSummariesData TEXT NOT NULL
      )
    ''');

    // Diary entries table
    await db.execute('''
      CREATE TABLE diary_entries(
        entryId TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        caseId TEXT,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        date TEXT NOT NULL,
        time TEXT,
        location TEXT,
        status TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (userId) REFERENCES users (userId)
      )
    ''');

    // Job sheets table
    await db.execute('''
      CREATE TABLE job_sheets(
        jobSheetId TEXT PRIMARY KEY,
        internId TEXT NOT NULL,
        assignedByAdvocateId TEXT NOT NULL,
        caseId TEXT,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        assignedDate TEXT NOT NULL,
        dueDate TEXT NOT NULL,
        status TEXT NOT NULL,
        deliverablesData TEXT NOT NULL,
        feedback TEXT,
        feedbackByAdvocateId TEXT,
        feedbackGivenAt TEXT,
        commentsData TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (internId) REFERENCES users (userId),
        FOREIGN KEY (assignedByAdvocateId) REFERENCES users (userId)
      )
    ''');

    // Yellow pages table
    await db.execute('''
      CREATE TABLE yellow_pages(
        entryId TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        contactDetailsData TEXT NOT NULL,
        specialtiesData TEXT NOT NULL,
        website TEXT,
        description TEXT NOT NULL,
        rating REAL,
        addedByUserId TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (addedByUserId) REFERENCES users (userId)
      )
    ''');
  }

  // User operations
  Future<void> saveUser(User user) async {
    final db = await database;
    await db.insert('users', {
      'userId': user.userId,
      'email': user.email,
      'passwordHash': user.passwordHash,
      'userType': user.userType.toString().split('.').last,
      'languagePreference': user.languagePreference,
      'createdAt': user.createdAt.toIso8601String(),
      'lastLogin': user.lastLogin?.toIso8601String(),
      'profileData': jsonEncode(user.profile.toJson()),
      'accessControlData': jsonEncode(user.accessControl.toJson()),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<User?> getUser(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'userId = ?',
      whereArgs: [userId],
    );

    if (maps.isNotEmpty) {
      final map = maps.first;
      return User(
        userId: map['userId'],
        email: map['email'],
        passwordHash: map['passwordHash'],
        userType: UserType.values.firstWhere(
          (e) => e.toString().split('.').last == map['userType'],
        ),
        languagePreference: map['languagePreference'],
        createdAt: DateTime.parse(map['createdAt']),
        lastLogin: map['lastLogin'] != null
            ? DateTime.parse(map['lastLogin'])
            : null,
        profile: UserProfile.fromJson(jsonDecode(map['profileData'])),
        accessControl: AccessControl.fromJson(
          jsonDecode(map['accessControlData']),
        ),
      );
    }
    return null;
  }

  Future<User?> getUserByEmail(String email) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: [email],
    );

    if (maps.isNotEmpty) {
      final map = maps.first;
      return User(
        userId: map['userId'],
        email: map['email'],
        passwordHash: map['passwordHash'],
        userType: UserType.values.firstWhere(
          (e) => e.toString().split('.').last == map['userType'],
        ),
        languagePreference: map['languagePreference'],
        createdAt: DateTime.parse(map['createdAt']),
        lastLogin: map['lastLogin'] != null
            ? DateTime.parse(map['lastLogin'])
            : null,
        profile: UserProfile.fromJson(jsonDecode(map['profileData'])),
        accessControl: AccessControl.fromJson(
          jsonDecode(map['accessControlData']),
        ),
      );
    }
    return null;
  }

  // Case operations
  Future<void> saveCase(Case caseData) async {
    final db = await database;
    await db.insert('cases', {
      'caseId': caseData.caseId,
      'caseType': caseData.caseType.toString().split('.').last,
      'status': caseData.status.toString().split('.').last,
      'filingData': jsonEncode(caseData.filing.toJson()),
      'registrationData': jsonEncode(caseData.registration.toJson()),
      'crnNumber': caseData.crnNumber,
      'uniqueId': caseData.uniqueId,
      'assignedUsersData': jsonEncode(
        caseData.assignedUsers.map((e) => e.toJson()).toList(),
      ),
      'createdAt': caseData.createdAt.toIso8601String(),
      'updatedAt': caseData.updatedAt.toIso8601String(),
      'clientDetailsData': jsonEncode(caseData.clientDetails.toJson()),
      'caseResearchFindingsData': jsonEncode(
        caseData.caseResearchFindings.map((e) => e.toJson()).toList(),
      ),
      'hearingsData': jsonEncode(
        caseData.hearings.map((e) => e.toJson()).toList(),
      ),
      'caseHistoryData': jsonEncode(
        caseData.caseHistory.map((e) => e.toJson()).toList(),
      ),
      'aiSummariesData': jsonEncode(
        caseData.aiSummaries.map((e) => e.toJson()).toList(),
      ),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<List<Case>> getCasesForUser(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('cases');

    List<Case> userCases = [];
    for (var map in maps) {
      final assignedUsers = jsonDecode(map['assignedUsersData']) as List;
      if (assignedUsers.any((user) => user['userId'] == userId)) {
        userCases.add(_mapToCase(map));
      }
    }
    return userCases;
  }

  Future<Case?> getCase(String caseId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cases',
      where: 'caseId = ?',
      whereArgs: [caseId],
    );

    if (maps.isNotEmpty) {
      return _mapToCase(maps.first);
    }
    return null;
  }

  Case _mapToCase(Map<String, dynamic> map) {
    return Case(
      caseId: map['caseId'],
      caseType: CaseType.values.firstWhere(
        (e) => e.toString().split('.').last == map['caseType'],
      ),
      status: CaseStatus.values.firstWhere(
        (e) => e.toString().split('.').last == map['status'],
      ),
      filing: Filing.fromJson(jsonDecode(map['filingData'])),
      registration: Registration.fromJson(jsonDecode(map['registrationData'])),
      crnNumber: map['crnNumber'],
      uniqueId: map['uniqueId'],
      assignedUsers: (jsonDecode(map['assignedUsersData']) as List)
          .map((e) => AssignedUser.fromJson(e))
          .toList(),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      clientDetails: ClientDetails.fromJson(
        jsonDecode(map['clientDetailsData']),
      ),
      caseResearchFindings:
          (jsonDecode(map['caseResearchFindingsData']) as List)
              .map((e) => CaseResearchFinding.fromJson(e))
              .toList(),
      hearings: (jsonDecode(map['hearingsData']) as List)
          .map((e) => Hearing.fromJson(e))
          .toList(),
      caseHistory: (jsonDecode(map['caseHistoryData']) as List)
          .map((e) => CaseHistoryEvent.fromJson(e))
          .toList(),
      aiSummaries: (jsonDecode(map['aiSummariesData']) as List)
          .map((e) => AISummary.fromJson(e))
          .toList(),
    );
  }

  // Diary entry operations
  Future<void> saveDiaryEntry(DiaryEntry entry) async {
    final db = await database;
    await db.insert(
      'diary_entries',
      entry.toJson(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<DiaryEntry>> getDiaryEntriesForUser(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'diary_entries',
      where: 'userId = ?',
      whereArgs: [userId],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) => DiaryEntry.fromJson(maps[i]));
  }

  // Job sheet operations
  Future<void> saveJobSheet(JobSheet jobSheet) async {
    final db = await database;
    await db.insert('job_sheets', {
      'jobSheetId': jobSheet.jobSheetId,
      'internId': jobSheet.internId,
      'assignedByAdvocateId': jobSheet.assignedByAdvocateId,
      'caseId': jobSheet.caseId,
      'title': jobSheet.title,
      'description': jobSheet.description,
      'assignedDate': jobSheet.assignedDate.toIso8601String(),
      'dueDate': jobSheet.dueDate.toIso8601String(),
      'status': jobSheet.status.toString().split('.').last,
      'deliverablesData': jsonEncode(
        jobSheet.deliverables.map((e) => e.toJson()).toList(),
      ),
      'feedback': jobSheet.feedback,
      'feedbackByAdvocateId': jobSheet.feedbackByAdvocateId,
      'feedbackGivenAt': jobSheet.feedbackGivenAt?.toIso8601String(),
      'commentsData': jsonEncode(
        jobSheet.comments.map((e) => e.toJson()).toList(),
      ),
      'createdAt': jobSheet.createdAt.toIso8601String(),
      'updatedAt': jobSheet.updatedAt.toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<List<JobSheet>> getJobSheetsForIntern(String internId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'job_sheets',
      where: 'internId = ?',
      whereArgs: [internId],
      orderBy: 'dueDate ASC',
    );

    return List.generate(maps.length, (i) => _mapToJobSheet(maps[i]));
  }

  JobSheet _mapToJobSheet(Map<String, dynamic> map) {
    return JobSheet(
      jobSheetId: map['jobSheetId'],
      internId: map['internId'],
      assignedByAdvocateId: map['assignedByAdvocateId'],
      caseId: map['caseId'],
      title: map['title'],
      description: map['description'],
      assignedDate: DateTime.parse(map['assignedDate']),
      dueDate: DateTime.parse(map['dueDate']),
      status: JobSheetStatus.values.firstWhere(
        (e) => e.toString().split('.').last == map['status'],
      ),
      deliverables: (jsonDecode(map['deliverablesData']) as List)
          .map((e) => Deliverable.fromJson(e))
          .toList(),
      feedback: map['feedback'],
      feedbackByAdvocateId: map['feedbackByAdvocateId'],
      feedbackGivenAt: map['feedbackGivenAt'] != null
          ? DateTime.parse(map['feedbackGivenAt'])
          : null,
      comments: (jsonDecode(map['commentsData']) as List)
          .map((e) => JobSheetComment.fromJson(e))
          .toList(),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }

  // Yellow pages operations
  Future<void> saveYellowPagesEntry(YellowPagesEntry entry) async {
    final db = await database;
    await db.insert('yellow_pages', {
      'entryId': entry.entryId,
      'name': entry.name,
      'category': entry.category,
      'contactDetailsData': jsonEncode(entry.contactDetails.toJson()),
      'specialtiesData': jsonEncode(entry.specialties),
      'website': entry.website,
      'description': entry.description,
      'rating': entry.rating,
      'addedByUserId': entry.addedByUserId,
      'createdAt': entry.createdAt.toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<List<YellowPagesEntry>> getAllYellowPagesEntries() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'yellow_pages',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => _mapToYellowPagesEntry(maps[i]));
  }

  YellowPagesEntry _mapToYellowPagesEntry(Map<String, dynamic> map) {
    return YellowPagesEntry(
      entryId: map['entryId'],
      name: map['name'],
      category: map['category'],
      contactDetails: YellowPagesContactDetails.fromJson(
        jsonDecode(map['contactDetailsData']),
      ),
      specialties: List<String>.from(jsonDecode(map['specialtiesData'])),
      website: map['website'],
      description: map['description'],
      rating: map['rating']?.toDouble(),
      addedByUserId: map['addedByUserId'],
      createdAt: DateTime.parse(map['createdAt']),
    );
  }
}
